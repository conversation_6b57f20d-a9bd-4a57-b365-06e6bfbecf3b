package com.deeppaas.task.data.impl;

import com.deeppaas.BusinessUtils;
import com.deeppaas.ExactnessConfig;
import com.deeppaas.KubaoConfig;
import com.deeppaas.common.exception.RunException;
import com.deeppaas.common.helper.*;
import com.deeppaas.task.config.convert.ProjectTaskConfigConvert;
import com.deeppaas.task.config.dto.ProjectTaskConfigDTO;
import com.deeppaas.task.config.service.ProjectTaskConfigService;
import com.deeppaas.task.config.service.ProjectTaskUploadService;
import com.deeppaas.task.data.convert.ProjectTaskFormDataConvert;
import com.deeppaas.task.data.dao.ProjectTaskFormDataDao;
import com.deeppaas.task.data.dto.ProjectTaskFormDataDTO;
import com.deeppaas.task.data.entity.ProjectTaskImageDataDO;
import com.deeppaas.task.data.entity.ProjectTaskPdfDataDO;
import com.deeppaas.task.data.entity.ProjectTaskFormDataDO;
import com.deeppaas.task.data.service.ProjectTaskFormDataService;
import com.deeppaas.task.data.service.ProjectTaskImageDataService;
import com.deeppaas.task.data.service.ProjectTaskPdfDataService;
import com.deeppaas.task.info.service.ProjectTaskInfoService;
import com.deeppaas.tool.service.ToolService;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.compress.utils.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ProjectTaskUploadImpl implements ProjectTaskUploadService {

    /**
     * 图像分件方式枚举
     */
    public enum ImageGroupingType {
        SINGLE_PIECE,           // 全部图像作为一件
        START_END_PAGE,         // 首页号+尾页号
        START_PAGE_COUNT,       // 首页号+页数
        PAGE_RANGE             // 起止页号（如"1-5"）
    }
    @Autowired
    private ProjectTaskInfoService projectTaskInfoService;
    @Autowired
    private ProjectTaskConfigService projectTaskConfigService;
    @Autowired
    private ProjectTaskConfigConvert projectTaskConfigConvert;
    @Autowired
    private ProjectTaskImageDataService projectTaskImageDataService;

    @Autowired
    private ProjectTaskFormDataService projectTaskFormDataService;
    @Autowired
    private ExactnessConfig exactnessConfig;
    @Autowired
    private ToolService toolService;
    @Autowired
    private ProjectTaskPdfDataService projectTaskPdfDataService;

    @Autowired
    private KubaoConfig kubaoConfig;

    @Autowired
    private ProjectTaskFormDataConvert projectTaskFormDataConvert;

    @Override
    public void excel(MultipartFile multipartFile, String taskConfigId) {
        ProjectTaskConfigDTO configDTO = projectTaskConfigService.findById(taskConfigId);
        String path = exactnessConfig.getPathExcel(configDTO.getTaskId());
        String imagePath = path + multipartFile.getOriginalFilename();


        FileHelper.existOrMkdir(new File(path));
        File file = new File(imagePath);
        try {
            multipartFile.transferTo(file);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        if (!FileHelper.isExcel(imagePath)) {
            file.delete();
            throw RunException.error("上传文件非excel类型！");
        }
        configDTO.setFilePath(imagePath);
        List<String> sheets = toolService.getSheets(imagePath);
        configDTO.setFileName(FileHelper.getFileName(file));
        configDTO.setSheetNames(JsonHelper.toJson(projectTaskConfigConvert.toSheetParam(sheets)));
        configDTO.setSheetName(sheets.get(0));
        projectTaskConfigService.save(projectTaskConfigConvert.toDo(configDTO));

    }

    @Override
    public void image(MultipartFile multipartFile, String taskConfigId, boolean dataKeyIs, String dataKey) {
        if (!FileHelper.isImage(multipartFile.getOriginalFilename()))
            return;
        projectTaskImageDataService.delTaskConfigId(taskConfigId);
        ProjectTaskConfigDTO configDTO = projectTaskConfigService.findById(taskConfigId);
        String path = exactnessConfig.getPathImage(configDTO.getTaskId());

        System.out.println("图片上传路径调试信息:");
        System.out.println("  基础路径: " + path);
        System.out.println("  数据键: " + dataKey);
        System.out.println("  是否使用数据键: " + dataKeyIs);
        System.out.println("  文件名: " + multipartFile.getOriginalFilename());

        //todo 规则取key(档号)
        String key = null;
        String imagePath = null;
        if (dataKeyIs) {
            key = dataKey;
            // 规范化路径分隔符
            imagePath = path + key + File.separator + multipartFile.getOriginalFilename();
            imagePath = imagePath.replace("/", File.separator).replace("\\", File.separator);
            // 处理重复的分隔符
            while (imagePath.contains(File.separator + File.separator)) {
                imagePath = imagePath.replace(File.separator + File.separator, File.separator);
            }
        } else {
            imagePath = path + dataKey;
            imagePath = imagePath.replace("/", File.separator).replace("\\", File.separator);
            while (imagePath.contains(File.separator + File.separator)) {
                imagePath = imagePath.replace(File.separator + File.separator, File.separator);
            }
        }

        System.out.println("  最终路径: " + imagePath);

        File file = new File(imagePath);
        // 确保父目录存在
        FileHelper.existOrMkdir(file.getParentFile());
        try {
            multipartFile.transferTo(file);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        if (!FileHelper.isImage(imagePath)) {
            file.delete();
        } else {
            configDTO.setFilePath(path);
            projectTaskConfigService.save(projectTaskConfigConvert.toDo(configDTO));
        }
        if (dataKeyIs) {
            buildImage(configDTO.getTaskId(), configDTO);
        } else {
            // buildImage2(configDTO.getTaskId(),configDTO);
        }


    }

    @Override
    public void pdf(MultipartFile multipartFile, String taskConfigId) {

        ProjectTaskConfigDTO configDTO = projectTaskConfigService.findById(taskConfigId);


        String path = exactnessConfig.getPathPdf(configDTO.getTaskId());

        String imagePath = path + File.separator + multipartFile.getOriginalFilename();
        FileHelper.existOrMkdir(new File(imagePath));
        File file = new File(imagePath);
        try {
            multipartFile.transferTo(file);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        if (!FileHelper.isPdf(imagePath)) {
            file.delete();
        } else {
            configDTO.setFilePath(path);
            projectTaskConfigService.save(projectTaskConfigConvert.toDo(configDTO));
        }
        buildPdf(configDTO.getTaskId(), configDTO);

    }


    @Override
    public void localExcel(String filePath, String taskConfigId) {
        if (!FileHelper.isExcel(filePath))
            throw RunException.error("上传文件非excel类型！");
        ProjectTaskConfigDTO configDTO = projectTaskConfigService.findById(taskConfigId);

        configDTO.setFilePath(filePath);
        List<String> sheets = toolService.getSheets(filePath);
        configDTO.setFileName(FileHelper.getFileName(new File(filePath)));
        configDTO.setSheetNames(JsonHelper.toJson(projectTaskConfigConvert.toSheetParam(sheets)));
        projectTaskConfigService.save(projectTaskConfigConvert.toDo(configDTO));
    }

    @Override
    public void localImage(String filePath, String taskConfigId) {
        System.out.println("📁📁📁 === 文件级处理API被调用 === 📁📁📁");
        System.out.println("📂 文件路径: " + filePath);
        System.out.println("🆔 任务配置ID: " + taskConfigId);
        System.out.println("📁📁📁 === 开始文件级处理流程 === 📁📁📁");

        ProjectTaskConfigDTO configDTO = projectTaskConfigService.findById(taskConfigId);

        configDTO.setFilePath(filePath);
        projectTaskConfigService.save(projectTaskConfigConvert.toDo(configDTO));
        buildImage(configDTO.getTaskId(), configDTO);
    }

    @Override
    public void localImageForJFJ(String filePath, String taskConfigId) {
        System.out.println("🏛️🏛️🏛️ === JFJ定制版文件级处理API被调用 === 🏛️🏛️🏛️");
        System.out.println("📂 文件路径: " + filePath);
        System.out.println("🆔 任务配置ID: " + taskConfigId);
        System.out.println("🏛️🏛️🏛️ === 开始JFJ定制版文件级处理流程 === 🏛️🏛️🏛️");

        ProjectTaskConfigDTO configDTO = projectTaskConfigService.findById(taskConfigId);
        configDTO.setFilePath(filePath);
        projectTaskConfigService.save(projectTaskConfigConvert.toDo(configDTO));

        //
        buildImageForJFJ(configDTO.getTaskId(), configDTO);
    }

    @Override
    public void localImageAndDataKey(String filePath, String taskConfigId, String dataKey, String partNo) {
        System.out.println("🌟🌟🌟🌟🌟 === 案卷级处理API被调用 === 🌟🌟🌟🌟🌟");
        System.out.println("📂 文件路径: " + filePath);
        System.out.println("🆔 任务配置ID: " + taskConfigId);
        System.out.println("🔑 数据键: " + dataKey);
        System.out.println("📄 件号: " + partNo);
        System.out.println("🌟🌟🌟🌟🌟 === 开始案卷级处理流程 === 🌟🌟🌟🌟🌟");

        ProjectTaskConfigDTO configDTO = projectTaskConfigService.findById(taskConfigId);

        configDTO.setFilePath(filePath);
        projectTaskConfigService.save(projectTaskConfigConvert.toDo(configDTO));
        buildImage(configDTO.getTaskId(), configDTO, dataKey, partNo);
    }

    @Override
    public void localPdf(String filePath, String taskConfigId) {
        ProjectTaskConfigDTO configDTO = projectTaskConfigService.findById(taskConfigId);

        configDTO.setFilePath(filePath);
        projectTaskConfigService.save(projectTaskConfigConvert.toDo(configDTO));
        buildPdf(configDTO.getTaskId(), configDTO);
    }

    private void buildImage(String taskId, ProjectTaskConfigDTO taskConfigDTO, String dataKey, String partNo) {
        System.out.println("🚀🚀🚀 === 案卷级处理入口方法被调用 === 🚀🚀🚀");
        System.out.println(String.format("📋 参数信息: taskId=%s, dataKey=%s, partNo=%s", taskId, dataKey, partNo));
        System.out.println(String.format("📋 配置信息: configId=%s, filePath=%s", taskConfigDTO.getId(), taskConfigDTO.getFilePath()));

        projectTaskImageDataService.delTaskConfigId(taskConfigDTO.getId());

        // 🔍 检测图像分件方式
        ImageGroupingType groupingType = detectImageGroupingType(taskConfigDTO);
        System.out.println("🔍 案卷级处理 - 检测到的分件方式: " + groupingType);

        // 🔍 根据分件方式选择处理策略
        switch (groupingType) {
            case SINGLE_PIECE:
                // 所有图像作为一件处理（现有逻辑）
                buildSinglePieceImages(taskId, taskConfigDTO, dataKey, partNo);
                break;

            case START_END_PAGE:
            case START_PAGE_COUNT:
            case PAGE_RANGE:
                // 根据Excel字段进行图像分组
                System.out.println("多件分组处理模式 - 开始执行案卷级图像分组");
                buildMultiPieceImages(taskId, taskConfigDTO, dataKey, partNo, groupingType);
                break;
        }
    }

    /**
     * 单件处理模式 - 所有图像作为一件处理（保持现有逻辑）
     */
    private void buildSinglePieceImages(String taskId, ProjectTaskConfigDTO taskConfigDTO, String dataKey, String partNo) {
        String urlRule = taskConfigDTO.getUrlRule();
        if (StringHelper.isEmpty(urlRule)) {
            throw RunException.error("【档号规则】不能为空！");
        }
        List<File> files = FileHelper.getFilesAndDirectory(taskConfigDTO.getFilePath(), null);

        Set<String> setPath = Sets.newHashSet();
        List<ProjectTaskImageDataDO> imageDataDOS = Lists.newArrayList();
        for (int i = 0; i < files.size(); i++) {
            File file = files.get(i);
            if (FileHelper.isImage(file.getName()) || file.isDirectory()) {
                String parentPath = file.getParent();
                if (file.isDirectory()) {
                    parentPath = file.getAbsolutePath();

                }
                if (!setPath.contains(parentPath)) {
                    ProjectTaskImageDataDO imageDataDO = new ProjectTaskImageDataDO();
                    imageDataDO.setDataKey(dataKey);
                    imageDataDO.setPartNumber(partNo);
                    imageDataDO.setTaskId(taskId);
                    imageDataDO.setTaskConfigId(taskConfigDTO.getId());
                    imageDataDO.setImageFilePath(parentPath);
                    List<File> files1 = Arrays.stream(new File(parentPath).listFiles()).toList();
                    imageDataDO.setImageCount(files1.stream().filter(item -> FileHelper.isImage(item.getName())).collect(Collectors.toList()).size());
                    imageDataDO.setCreateTime(LocalDateTime.now());
                    imageDataDO.setImageNames(files1.stream().map(File::getName).collect(Collectors.toList()));
                    List<File> filelists = FileHelper.getFilesAndDirectory(parentPath, null);
                    imageDataDO.setImageSize(filelists.stream().mapToLong(File::length).sum());
                    imageDataDOS.add(imageDataDO);
                    setPath.add(parentPath);
                }

            }
        }

        projectTaskImageDataService.saves(imageDataDOS);
    }

    /**
     * 多件处理模式 - 根据Excel字段进行图像分组
     */
    private void buildMultiPieceImages(String taskId, ProjectTaskConfigDTO taskConfigDTO,
                                     String dataKey, String partNo, ImageGroupingType groupingType) {
        System.out.println("=== 开始多件分组处理 ===");
        System.out.println(String.format("任务ID: %s, 数据键: %s, 件号: %s, 分组类型: %s",
            taskId, dataKey, partNo, groupingType));

        try {
            // 🔍 1. 获取所有图像文件
            List<File> files = FileHelper.getFilesAndDirectory(taskConfigDTO.getFilePath(), null);
            List<ProjectTaskImageDataDO> allImages = buildImageDataList(files, taskId, taskConfigDTO);
            System.out.println(String.format("发现图像文件: %d个", allImages.size()));

            // 🔍 2. 获取Excel数据
            List<ProjectTaskFormDataDTO> formDataDTOList = projectTaskFormDataService.findByTaskId(taskId);
            List<ProjectTaskFormDataDO> formDataList = formDataDTOList.stream()
                .map(dto -> projectTaskFormDataConvert.toDo(dto))
                .collect(Collectors.toList());
            System.out.println(String.format("Excel条目数量: %d个", formDataList.size()));

            if (formDataList.isEmpty()) {
                System.err.println("警告: 未找到Excel数据，使用单件处理模式");
                buildSinglePieceImages(taskId, taskConfigDTO, dataKey, partNo);
                return;
            }

            // 🔍 3. 执行案卷级图像分组处理
            List<ProjectTaskImageDataDO> groupedImages = processCaseLevelImageGrouping(
                allImages, formDataList, taskConfigDTO);

            // 🔍 4. 保存分组后的图像数据
            if (!groupedImages.isEmpty()) {
                projectTaskImageDataService.saves(groupedImages);
                System.out.println(String.format("多件分组处理完成，保存图像数据: %d条", groupedImages.size()));
            } else {
                System.err.println("警告: 分组处理未产生有效结果，回退到单件处理");
                buildSinglePieceImages(taskId, taskConfigDTO, dataKey, partNo);
            }

        } catch (Exception e) {
            System.err.println("多件分组处理异常: " + e.getMessage());
            e.printStackTrace();
            System.out.println("回退到单件处理模式");
            buildSinglePieceImages(taskId, taskConfigDTO, dataKey, partNo);
        }
    }

    /**
     * 构建图像数据列表
     */
    private List<ProjectTaskImageDataDO> buildImageDataList(List<File> files, String taskId, ProjectTaskConfigDTO taskConfigDTO) {
        Set<String> setPath = Sets.newHashSet();
        List<ProjectTaskImageDataDO> imageDataList = Lists.newArrayList();

        for (File file : files) {
            if (FileHelper.isImage(file.getName()) || file.isDirectory()) {
                String parentPath = file.getParent();
                if (file.isDirectory()) {
                    parentPath = file.getAbsolutePath();
                }

                if (!setPath.contains(parentPath)) {
                    ProjectTaskImageDataDO imageDataDO = new ProjectTaskImageDataDO();
                    imageDataDO.setTaskId(taskId);
                    imageDataDO.setTaskConfigId(taskConfigDTO.getId());
                    imageDataDO.setImageFilePath(parentPath);

                    List<File> files1 = Arrays.stream(new File(parentPath).listFiles()).toList();
                    imageDataDO.setImageCount(files1.stream().filter(item -> FileHelper.isImage(item.getName())).collect(Collectors.toList()).size());
                    imageDataDO.setCreateTime(LocalDateTime.now());
                    imageDataDO.setImageNames(files1.stream().map(File::getName).collect(Collectors.toList()));
                    List<File> filelists = FileHelper.getFilesAndDirectory(parentPath, null);
                    imageDataDO.setImageSize(filelists.stream().mapToLong(File::length).sum());

                    imageDataList.add(imageDataDO);
                    setPath.add(parentPath);
                }
            }
        }

        return imageDataList;
    }

    public void buildImageForJFJ(String taskId, ProjectTaskConfigDTO taskConfigDTO) {
        System.out.println("🏗️🏗️🏗️ === JFJ定制版图像构建方法被调用 === 🏗️🏗️🏗️");
        System.out.println("📋 任务ID: " + taskId);
        System.out.println("📂 文件路径: " + taskConfigDTO.getFilePath());
        System.out.println("🏗️🏗️🏗️ === 开始JFJ定制版图像构建流程 === 🏗️🏗️🏗️");

        projectTaskImageDataService.delTaskConfigId(taskConfigDTO.getId());
        String dataKeyRule = kubaoConfig.getDataKeyRule();
        String partRule = kubaoConfig.getPartRule();

        System.out.println("📝 正则规则配置:");
        System.out.println("  档号规则: " + dataKeyRule);
        System.out.println("  件号规则: " + partRule);
        if (StringHelper.isEmpty(dataKeyRule)) {
            throw RunException.error("【配置文件种正则-档号规则】不能为空！");
        }
        //获取所有文件夹
        List<File> files = FileHelper.getFilesAndDirectory(taskConfigDTO.getFilePath(), null);
        File fileRoot = new File(taskConfigDTO.getFilePath());
        String fileRootPath = fileRoot.getAbsolutePath();
        List<ProjectTaskImageDataDO> imageDataDOS = Lists.newArrayList();
        Map<String, List<File>> fileMaps = new HashMap<>();
        files.forEach(item -> {
            String absolutePath = item.getAbsolutePath();
            absolutePath = FileHelper.removeRootDirectory(fileRootPath, absolutePath);
            String dateKey = PatternHelper.get(absolutePath, dataKeyRule);
            if (StringHelper.isEmpty(dateKey)) {
                throw RunException.error("未找到正确档号，请检查正则：" + item.getAbsolutePath());
            }
            String partKey = null;
            if (Objects.equals(taskConfigDTO.getPieceHaveIs(), BoolHelper.INT_TRUE)) {
                partKey = PatternHelper.get(absolutePath, partRule);
            }
            dateKey = StringHelper.isEmpty(partKey) ? dateKey : dateKey + "-" + partKey;

            List<File> fileMap = fileMaps.get(dateKey);
            if (CollectionUtils.isEmpty(fileMap)) {
                fileMap = Lists.newArrayList();
            }
            fileMap.add(item);
            fileMaps.put(dateKey, fileMap);
        });
        fileMaps.forEach((k, y) -> {
            File file = y.get(0);

            String absolutePath = file.getAbsolutePath();
            absolutePath = FileHelper.removeRootDirectory(fileRootPath, absolutePath);
            String dataKey = PatternHelper.get(absolutePath, dataKeyRule);
            String partKey = null;
            if (Objects.equals(taskConfigDTO.getPieceHaveIs(), BoolHelper.INT_TRUE)) {
                partKey = PatternHelper.get(absolutePath, partRule);
            }
            buildForJfJ(taskConfigDTO, dataKey, partKey, y, imageDataDOS);
        });
//        Map<String,List<File>> fileMap=files.stream().filter(item->item.getParentFile().isDirectory()).collect(Collectors.groupingBy(File::getParent));
//        fileMap.forEach((k,y)->{
//
//                buildForJfJ(file, dataKey,part, imageDataDOS);
//
//        });

//        String filePath=fileRoot.getAbsolutePath();
//        filePath=filePath.substring(fileRootPath.length()+1);

        projectTaskImageDataService.saves(imageDataDOS);

    }


    public void buildImage(String taskId, ProjectTaskConfigDTO taskConfigDTO) {
        projectTaskImageDataService.delTaskConfigId(taskConfigDTO.getId());
        String urlRule = taskConfigDTO.getUrlRule();

        if (StringHelper.isEmpty(urlRule)) {
            throw RunException.error("【档号规则】不能为空！");
        }
        //获取所有文件夹
        List<File> files = FileHelper.getFilesAndDirectory(taskConfigDTO.getFilePath(), null);
        files = files.stream().map(item -> item.getParentFile()).filter(item -> item.isDirectory()).collect(Collectors.toSet()).stream().toList();


        List<ProjectTaskImageDataDO> imageDataDOS = Lists.newArrayList();
        for (int i = 0; i < files.size(); i++) {
            File file = files.get(i);
            build(file, urlRule, taskConfigDTO, imageDataDOS);
        }

        projectTaskImageDataService.saves(imageDataDOS);

    }

    public void buildForJfJ(ProjectTaskConfigDTO taskConfigDTO, String dataKey, String partNumber, List<File> files, List<ProjectTaskImageDataDO> imageDataDOS) {
        File file = files.get(0);
        //TODO
        ProjectTaskImageDataDO imageDataDO = new ProjectTaskImageDataDO();
        imageDataDO.setDataKey(dataKey);
        imageDataDO.setPartNumber(partNumber);
        imageDataDO.setTaskId(taskConfigDTO.getTaskId());
        imageDataDO.setTaskConfigId(taskConfigDTO.getId());
        imageDataDO.setImageFilePath(file.getParent());

        imageDataDO.setImageCount(files.stream().filter(item -> FileHelper.isImage(item.getName())).collect(Collectors.toList()).size());
        imageDataDO.setCreateTime(LocalDateTime.now());
        imageDataDO.setSuccess(BoolHelper.INT_FALSE);
        imageDataDO.setImageSize(files.stream().mapToLong(File::length).sum());
        imageDataDO.setImageNames(files.stream().map(item -> item.getName()).collect(Collectors.toList()));
        imageDataDOS.add(imageDataDO);


    }

    public void build(File file, String urlRule, ProjectTaskConfigDTO taskConfigDTO, List<ProjectTaskImageDataDO> imageDataDOS) {
        if (FileHelper.isImage(file.getName()) || file.isDirectory()) {
            String dataKey = BusinessUtils.buildKeyForDataKey(urlRule, file);
            //判断包不包含件号
            String partNumber = "";
            if (Objects.equals(taskConfigDTO.getPieceHaveIs(), BoolHelper.INT_TRUE) && dataKey.indexOf("-") > 0) {
                partNumber = dataKey.substring(dataKey.lastIndexOf("-") + 1);
                dataKey = dataKey.substring(0, dataKey.lastIndexOf("-"));
            }


            ProjectTaskImageDataDO imageDataDO = new ProjectTaskImageDataDO();
            imageDataDO.setDataKey(dataKey);
            imageDataDO.setPartNumber(partNumber);
            imageDataDO.setTaskId(taskConfigDTO.getTaskId());
            imageDataDO.setTaskConfigId(taskConfigDTO.getId());
            imageDataDO.setImageFilePath(file.getAbsolutePath());
            List<File> files1 = Arrays.stream(file.listFiles()).toList();
            imageDataDO.setImageCount(files1.stream().filter(item -> FileHelper.isImage(item.getName())).collect(Collectors.toList()).size());
            imageDataDO.setCreateTime(LocalDateTime.now());
            imageDataDO.setSuccess(BoolHelper.INT_FALSE);
            imageDataDO.setImageSize(files1.stream().mapToLong(File::length).sum());
            imageDataDO.setImageNames(files1.stream().map(File::getName).collect(Collectors.toList()));
            imageDataDOS.add(imageDataDO);


        }
    }

    @Override
    public void ofd(MultipartFile multipartFile, String taskConfigId) {

        ProjectTaskConfigDTO configDTO = projectTaskConfigService.findById(taskConfigId);


        String path = exactnessConfig.getPathPdf(configDTO.getTaskId());

        String imagePath = path + File.separator + multipartFile.getOriginalFilename();
        FileHelper.existOrMkdir(new File(imagePath));
        File file = new File(imagePath);
        try {
            multipartFile.transferTo(file);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        if (!multipartFile.getOriginalFilename().contains(".ofd")) {
            file.delete();
        } else {
            configDTO.setFilePath(path);
            projectTaskConfigService.save(projectTaskConfigConvert.toDo(configDTO));
        }
        buildOfd(configDTO.getTaskId(), configDTO);

    }

    @Override
    public void localOfd(String filePath, String taskConfigId) {
        ProjectTaskConfigDTO configDTO = projectTaskConfigService.findById(taskConfigId);

        configDTO.setFilePath(filePath);
        projectTaskConfigService.save(projectTaskConfigConvert.toDo(configDTO));
        buildOfd(configDTO.getTaskId(), configDTO);
    }


    private void buildPdf(String taskId, ProjectTaskConfigDTO taskConfigDTO) {

        projectTaskPdfDataService.delTaskConfigId(taskConfigDTO.getId());


        String urlRule = taskConfigDTO.getUrlRule();
        if (StringHelper.isEmpty(urlRule)) {
            throw RunException.error("【档号规则】不能为空！");
        }
        List<File> files = FileHelper.getFilesAndDirectory(taskConfigDTO.getFilePath(), null);


        Set<String> setPath = Sets.newHashSet();
        List<ProjectTaskPdfDataDO> imageDataDOS = Lists.newArrayList();
        for (int i = 0; i < files.size(); i++) {
            File file = files.get(i);
            if (FileHelper.isPdf(file.getName())) {
                String dataKey = FileHelper.getFileName(file);
                //判断包不包含件号
                String partNumber = "";
                if (Objects.equals(taskConfigDTO.getPieceHaveIs(), BoolHelper.INT_TRUE) && dataKey.indexOf("-") > 0) {
                    partNumber = dataKey.substring(dataKey.lastIndexOf("-") + 1);
                    dataKey = dataKey.substring(0, dataKey.lastIndexOf("-"));
                }


                if (!setPath.contains(file.getAbsolutePath())) {
                    ProjectTaskPdfDataDO imageDataDO = new ProjectTaskPdfDataDO();
                    imageDataDO.setDataKey(dataKey);
                    imageDataDO.setPartNumber(partNumber);
                    imageDataDO.setTaskId(taskId);
                    imageDataDO.setTaskConfigId(taskConfigDTO.getId());
                    imageDataDO.setPdfFilePath(file.getAbsolutePath());
                    imageDataDO.setPdfCount(PdfHelper.getImageCount(file));
                    imageDataDO.setCreateTime(LocalDateTime.now());
                    imageDataDO.setFileType("pdf");
                    imageDataDOS.add(imageDataDO);
                    setPath.add(file.getAbsolutePath());
                }

            }
        }
        projectTaskPdfDataService.saves(imageDataDOS);
    }

    private void buildOfd(String taskId, ProjectTaskConfigDTO taskConfigDTO) {

        projectTaskPdfDataService.delTaskConfigId(taskConfigDTO.getId());


        String urlRule = taskConfigDTO.getUrlRule();
        if (StringHelper.isEmpty(urlRule)) {
            throw RunException.error("【档号规则】不能为空！");
        }
        List<File> files = FileHelper.getFilesAndDirectory(taskConfigDTO.getFilePath(), null);


        Set<String> setPath = Sets.newHashSet();
        List<ProjectTaskPdfDataDO> imageDataDOS = Lists.newArrayList();
        for (int i = 0; i < files.size(); i++) {
            File file = files.get(i);
            if (FileHelper.isOfd(file.getName())) {
                String dataKey = FileHelper.getFileName(file);
                //判断包不包含件号
                String partNumber = "";
                if (Objects.equals(taskConfigDTO.getPieceHaveIs(), BoolHelper.INT_TRUE) && dataKey.indexOf("-") > 0) {
                    partNumber = dataKey.substring(dataKey.lastIndexOf("-") + 1);
                    dataKey = dataKey.substring(0, dataKey.lastIndexOf("-"));
                }


                if (!setPath.contains(file.getAbsolutePath())) {
                    ProjectTaskPdfDataDO imageDataDO = new ProjectTaskPdfDataDO();
                    imageDataDO.setDataKey(dataKey);
                    imageDataDO.setPartNumber(partNumber);
                    imageDataDO.setTaskId(taskId);
                    imageDataDO.setTaskConfigId(taskConfigDTO.getId());
                    imageDataDO.setPdfFilePath(file.getAbsolutePath());
                    imageDataDO.setCreateTime(LocalDateTime.now());
                    imageDataDO.setFileType("ofd");
                    imageDataDOS.add(imageDataDO);
                    setPath.add(file.getAbsolutePath());
                }

            }
        }
        projectTaskPdfDataService.saves(imageDataDOS);
    }

    /**
     * 检测图像分件方式
     * 根据字段库配置中的字段映射来判断使用哪种分件方式
     *
     * @param taskConfigDTO 任务配置
     * @return 图像分件方式类型
     */
    private ImageGroupingType detectImageGroupingType(ProjectTaskConfigDTO taskConfigDTO) {
        System.out.println("🔍🔍🔍 === 开始检测图像分件方式 === 🔍🔍🔍");

        // 🔍 从字段库配置中查找关键字段
        Map<String, String> fieldMapping = taskConfigDTO.buildRuleMappingMap();

        System.out.println("📋 字段映射信息:");
        if (fieldMapping == null || fieldMapping.isEmpty()) {
            System.out.println("  ❌ 字段映射为空，使用单件处理模式");
            return ImageGroupingType.SINGLE_PIECE;
        } else {
            System.out.println("  ✅ 字段映射数量: " + fieldMapping.size());
            fieldMapping.forEach((key, value) ->
                System.out.println("    " + key + " -> " + value));
        }

        // 🔍 检查是否存在分件相关字段
        boolean hasStartPage = containsFieldValue(fieldMapping, "首页号");
        boolean hasEndPage = containsFieldValue(fieldMapping, "尾页号");
        boolean hasPageCount = containsFieldValue(fieldMapping, "页数");
        boolean hasPageRange = containsFieldValue(fieldMapping, "起止页号");

        System.out.println("🔍 分件字段检测结果:");
        System.out.println("  首页号: " + (hasStartPage ? "✅ 存在" : "❌ 不存在"));
        System.out.println("  尾页号: " + (hasEndPage ? "✅ 存在" : "❌ 不存在"));
        System.out.println("  页数: " + (hasPageCount ? "✅ 存在" : "❌ 不存在"));
        System.out.println("  起止页号: " + (hasPageRange ? "✅ 存在" : "❌ 不存在"));

        // 🔍 按优先级确定处理方式
        if (hasPageRange) {
            System.out.println("🎯 检测到起止页号字段，使用PAGE_RANGE模式");
            return ImageGroupingType.PAGE_RANGE;
        } else if (hasStartPage && hasEndPage) {
            System.out.println("🎯 检测到首页号+尾页号字段，使用START_END_PAGE模式");
            return ImageGroupingType.START_END_PAGE;
        } else if (hasStartPage && hasPageCount) {
            System.out.println("🎯 检测到首页号+页数字段，使用START_PAGE_COUNT模式");
            return ImageGroupingType.START_PAGE_COUNT;
        } else {
            System.out.println("🎯 未检测到分件相关字段，使用单件处理模式");
            return ImageGroupingType.SINGLE_PIECE;
        }
    }

    /**
     * 检查字段映射中是否包含指定的字段值
     *
     * @param fieldMapping 字段映射 Map<fieldId, fieldName>
     * @param targetFieldName 目标字段名称
     * @return 是否包含该字段
     */
    private boolean containsFieldValue(Map<String, String> fieldMapping, String targetFieldName) {
        return fieldMapping.values().stream()
                .anyMatch(fieldName -> fieldName != null && fieldName.contains(targetFieldName));
    }

    /**
     * 根据字段值反向查找字段ID
     *
     * @param fieldMapping 字段映射
     * @param targetFieldName 目标字段名称
     * @return 对应的字段ID，如果未找到返回null
     */
    private String getFieldIdByValue(Map<String, String> fieldMapping, String targetFieldName) {
        return fieldMapping.entrySet().stream()
                .filter(entry -> entry.getValue() != null && entry.getValue().contains(targetFieldName))
                .map(Map.Entry::getKey)
                .findFirst()
                .orElse(null);
    }

    /**
     * 页号范围信息类
     */
    public static class PageRangeInfo {
        private int startPage;
        private int endPage;
        private int pageCount;
        private boolean isValid;
        private String errorMessage;

        public PageRangeInfo(int startPage, int endPage) {
            this.startPage = startPage;
            this.endPage = endPage;
            this.pageCount = endPage - startPage + 1;
            this.isValid = startPage > 0 && endPage >= startPage;
            if (!isValid) {
                this.errorMessage = String.format("无效的页号范围: 起始页[%d], 结束页[%d]", startPage, endPage);
            }
        }

        public PageRangeInfo(String errorMessage) {
            this.isValid = false;
            this.errorMessage = errorMessage;
            this.startPage = 1;
            this.endPage = Integer.MAX_VALUE;
            this.pageCount = Integer.MAX_VALUE;
        }

        // Getters
        public int getStartPage() { return startPage; }
        public int getEndPage() { return endPage; }
        public int getPageCount() { return pageCount; }
        public boolean isValid() { return isValid; }
        public String getErrorMessage() { return errorMessage; }

        @Override
        public String toString() {
            if (isValid) {
                return String.format("页号范围[%d-%d], 共%d页", startPage, endPage, pageCount);
            } else {
                return String.format("无效范围: %s", errorMessage);
            }
        }
    }

    /**
     * 页号范围解析器
     * 支持三种解析方式：首页号+尾页号、首页号+页数、起止页号格式
     *
     * @param formData Excel表单数据
     * @param groupingType 分件方式类型
     * @param taskConfigDTO 任务配置
     * @return 页号范围信息
     */
    private PageRangeInfo parsePageRange(ProjectTaskFormDataDO formData, ImageGroupingType groupingType, ProjectTaskConfigDTO taskConfigDTO) {
        try {
            // 🔍 解析Excel数据JSON
            String taskJson = formData.getTaskJson();
            if (StringHelper.isEmpty(taskJson)) {
                return new PageRangeInfo("Excel数据为空");
            }

            Map<String, Object> taskData = JsonHelper.json2map(taskJson);
            Map<String, String> fieldMapping = taskConfigDTO.buildRuleMappingMap();

            System.out.println(String.format("解析页号范围 - 分件方式: %s, 数据行: %d", groupingType, formData.getRowNum()));

            switch (groupingType) {
                case START_END_PAGE:
                    return parseStartEndPage(taskData, fieldMapping);

                case START_PAGE_COUNT:
                    return parseStartPageCount(taskData, fieldMapping);

                case PAGE_RANGE:
                    return parsePageRangeFormat(taskData, fieldMapping);

                default:
                    return new PageRangeInfo(1, Integer.MAX_VALUE); // 单件处理
            }

        } catch (Exception e) {
            System.err.println("页号范围解析异常: " + e.getMessage());
            e.printStackTrace();
            return new PageRangeInfo("解析异常: " + e.getMessage());
        }
    }

    /**
     * 解析首页号+尾页号模式
     *
     * @param taskData Excel行数据
     * @param fieldMapping 字段映射
     * @return 页号范围信息
     */
    private PageRangeInfo parseStartEndPage(Map<String, Object> taskData, Map<String, String> fieldMapping) {
        try {
            // 🔍 查找首页号字段
            String startPageField = findFieldByKeyword(fieldMapping, "首页号");
            String endPageField = findFieldByKeyword(fieldMapping, "尾页号");

            if (startPageField == null || endPageField == null) {
                return new PageRangeInfo("未找到首页号或尾页号字段");
            }

            Object startPageValue = taskData.get(startPageField);
            Object endPageValue = taskData.get(endPageField);

            System.out.println(String.format("首页号+尾页号解析 - 首页号字段[%s]: %s, 尾页号字段[%s]: %s",
                startPageField, startPageValue, endPageField, endPageValue));

            int startPage = parsePageNumber(startPageValue, "首页号");
            int endPage = parsePageNumber(endPageValue, "尾页号");

            return new PageRangeInfo(startPage, endPage);

        } catch (Exception e) {
            return new PageRangeInfo("首页号+尾页号解析失败: " + e.getMessage());
        }
    }

    /**
     * 解析首页号+页数模式
     *
     * @param taskData Excel行数据
     * @param fieldMapping 字段映射
     * @return 页号范围信息
     */
    private PageRangeInfo parseStartPageCount(Map<String, Object> taskData, Map<String, String> fieldMapping) {
        try {
            // 🔍 查找首页号和页数字段
            String startPageField = findFieldByKeyword(fieldMapping, "首页号");
            String pageCountField = findFieldByKeyword(fieldMapping, "页数");

            if (startPageField == null || pageCountField == null) {
                return new PageRangeInfo("未找到首页号或页数字段");
            }

            Object startPageValue = taskData.get(startPageField);
            Object pageCountValue = taskData.get(pageCountField);

            System.out.println(String.format("首页号+页数解析 - 首页号字段[%s]: %s, 页数字段[%s]: %s",
                startPageField, startPageValue, pageCountField, pageCountValue));

            int startPage = parsePageNumber(startPageValue, "首页号");
            int pageCount = parsePageNumber(pageCountValue, "页数");

            if (pageCount <= 0) {
                return new PageRangeInfo("页数必须大于0");
            }

            int endPage = startPage + pageCount - 1;
            return new PageRangeInfo(startPage, endPage);

        } catch (Exception e) {
            return new PageRangeInfo("首页号+页数解析失败: " + e.getMessage());
        }
    }

    /**
     * 解析起止页号格式（如"1-5", "10-15"）
     *
     * @param taskData Excel行数据
     * @param fieldMapping 字段映射
     * @return 页号范围信息
     */
    private PageRangeInfo parsePageRangeFormat(Map<String, Object> taskData, Map<String, String> fieldMapping) {
        try {
            // 🔍 查找起止页号字段
            String pageRangeField = findFieldByKeyword(fieldMapping, "起止页号");

            if (pageRangeField == null) {
                return new PageRangeInfo("未找到起止页号字段");
            }

            Object pageRangeValue = taskData.get(pageRangeField);

            System.out.println(String.format("起止页号解析 - 字段[%s]: %s", pageRangeField, pageRangeValue));

            if (pageRangeValue == null || StringHelper.isEmpty(pageRangeValue.toString())) {
                return new PageRangeInfo("起止页号值为空");
            }

            String pageRangeStr = pageRangeValue.toString().trim();

            // 🔍 支持多种格式：1-5, 1~5, 1至5, 1到5
            String[] separators = {"-", "~", "至", "到"};
            String[] parts = null;

            for (String separator : separators) {
                if (pageRangeStr.contains(separator)) {
                    parts = pageRangeStr.split(separator);
                    break;
                }
            }

            if (parts == null || parts.length != 2) {
                return new PageRangeInfo("起止页号格式不正确，支持格式：1-5, 1~5, 1至5, 1到5");
            }

            int startPage = parsePageNumber(parts[0].trim(), "起始页");
            int endPage = parsePageNumber(parts[1].trim(), "结束页");

            return new PageRangeInfo(startPage, endPage);

        } catch (Exception e) {
            return new PageRangeInfo("起止页号解析失败: " + e.getMessage());
        }
    }

    /**
     * 根据关键词查找字段名
     *
     * @param fieldMapping 字段映射
     * @param keyword 关键词
     * @return 匹配的字段名，如果未找到返回null
     */
    private String findFieldByKeyword(Map<String, String> fieldMapping, String keyword) {
        return fieldMapping.entrySet().stream()
                .filter(entry -> entry.getValue() != null && entry.getValue().contains(keyword))
                .map(Map.Entry::getValue)
                .findFirst()
                .orElse(null);
    }

    /**
     * 解析页号数值
     *
     * @param value 页号值对象
     * @param fieldName 字段名称（用于错误信息）
     * @return 解析后的页号数值
     * @throws NumberFormatException 如果解析失败
     */
    private int parsePageNumber(Object value, String fieldName) throws NumberFormatException {
        if (value == null) {
            throw new NumberFormatException(fieldName + "值为空");
        }

        String valueStr = value.toString().trim();
        if (StringHelper.isEmpty(valueStr)) {
            throw new NumberFormatException(fieldName + "值为空字符串");
        }

        try {
            // 🔍 移除可能的非数字字符（如"第1页"中的"第"和"页"）
            String cleanValue = valueStr.replaceAll("[^0-9]", "");
            if (StringHelper.isEmpty(cleanValue)) {
                throw new NumberFormatException(fieldName + "中没有找到数字");
            }

            int pageNumber = Integer.parseInt(cleanValue);
            if (pageNumber <= 0) {
                throw new NumberFormatException(fieldName + "必须大于0");
            }

            System.out.println(String.format("页号解析成功 - %s: [%s] -> %d", fieldName, valueStr, pageNumber));
            return pageNumber;

        } catch (NumberFormatException e) {
            System.err.println(String.format("页号解析失败 - %s: [%s], 错误: %s", fieldName, valueStr, e.getMessage()));
            throw new NumberFormatException(String.format("%s格式错误: [%s]", fieldName, valueStr));
        }
    }

    /**
     * 案卷级图像分组处理
     * 根据Excel数据和页号范围信息，将图像按文件进行分组
     *
     * @param imageDataList 图像数据列表
     * @param formDataList Excel表单数据列表
     * @param taskConfigDTO 任务配置
     * @return 分组后的图像数据列表
     */
    public List<ProjectTaskImageDataDO> processCaseLevelImageGrouping(
            List<ProjectTaskImageDataDO> imageDataList,
            List<ProjectTaskFormDataDO> formDataList,
            ProjectTaskConfigDTO taskConfigDTO) {

        System.out.println("=== 开始案卷级图像分组处理 ===");
        System.out.println(String.format("输入图像数量: %d, Excel条目数量: %d",
            imageDataList.size(), formDataList.size()));

        try {
            // 🔍 1. 检测图像分组类型
            ImageGroupingType groupingType = detectImageGroupingType(taskConfigDTO);
            System.out.println("检测到的分组类型: " + groupingType);

            if (groupingType == ImageGroupingType.SINGLE_PIECE) {
                System.out.println("使用单件处理模式，无需分组");
                return imageDataList;
            }

            // 🔍 2. 为每个Excel条目解析页号范围
            List<DocumentGroup> documentGroups = new ArrayList<>();
            for (ProjectTaskFormDataDO formData : formDataList) {
                PageRangeInfo pageRange = parsePageRange(formData, groupingType, taskConfigDTO);

                if (pageRange.isValid()) {
                    DocumentGroup group = new DocumentGroup(formData, pageRange);
                    documentGroups.add(group);
                    System.out.println(String.format("Excel行%d: 页号范围[%d-%d], 共%d页",
                        formData.getRowNum(), pageRange.getStartPage(), pageRange.getEndPage(), pageRange.getPageCount()));
                } else {
                    System.err.println(String.format("Excel行%d页号范围解析失败: %s",
                        formData.getRowNum(), pageRange.getErrorMessage()));
                }
            }

            // 🔍 3. 根据页号范围分组图像
            List<ProjectTaskImageDataDO> groupedImages = groupImagesByPageRange(imageDataList, documentGroups);

            System.out.println(String.format("分组完成，输出图像数量: %d", groupedImages.size()));
            return groupedImages;

        } catch (Exception e) {
            System.err.println("案卷级图像分组处理异常: " + e.getMessage());
            e.printStackTrace();
            // 🔍 异常情况下返回原始图像列表
            return imageDataList;
        }
    }

    /**
     * 根据页号范围分组图像
     *
     * @param imageDataList 原始图像数据列表
     * @param documentGroups 文档分组信息
     * @return 分组后的图像数据列表
     */
    private List<ProjectTaskImageDataDO> groupImagesByPageRange(
            List<ProjectTaskImageDataDO> imageDataList,
            List<DocumentGroup> documentGroups) {

        List<ProjectTaskImageDataDO> result = new ArrayList<>();

        // 🔍 按图像文件路径排序（假设文件路径包含页号信息）
        List<ProjectTaskImageDataDO> sortedImages = imageDataList.stream()
                .sorted((img1, img2) -> {
                    String path1 = extractPageNumberFromFileName(img1.getImageFilePath());
                    String path2 = extractPageNumberFromFileName(img2.getImageFilePath());
                    return path1.compareTo(path2);
                })
                .collect(Collectors.toList());

        System.out.println("图像排序完成，开始按页号范围分组");

        for (DocumentGroup group : documentGroups) {
            PageRangeInfo pageRange = group.getPageRange();
            ProjectTaskFormDataDO formData = group.getFormData();

            System.out.println(String.format("处理文档组 - Excel行%d: 页号范围[%d-%d]",
                formData.getRowNum(), pageRange.getStartPage(), pageRange.getEndPage()));

            // 🔍 查找对应页号范围的图像
            List<ProjectTaskImageDataDO> groupImages = new ArrayList<>();
            for (int pageNum = pageRange.getStartPage(); pageNum <= pageRange.getEndPage(); pageNum++) {
                ProjectTaskImageDataDO matchedImage = findImageByPageNumber(sortedImages, pageNum);
                if (matchedImage != null) {
                    // 🔍 创建新的图像数据对象，设置正确的dataKey和partNumber
                    ProjectTaskImageDataDO groupedImage = cloneImageData(matchedImage);
                    groupedImage.setDataKey(formData.getDataKey());
                    groupedImage.setPartNumber(formData.getPartNumber());

                    groupImages.add(groupedImage);
                    System.out.println(String.format("  匹配图像: 页号%d -> %s", pageNum, matchedImage.getImageFilePath()));
                } else {
                    System.err.println(String.format("  警告: 未找到页号%d对应的图像", pageNum));
                }
            }

            result.addAll(groupImages);
            System.out.println(String.format("文档组处理完成，添加%d张图像", groupImages.size()));
        }

        return result;
    }

    /**
     * 文档分组信息类
     */
    private static class DocumentGroup {
        private final ProjectTaskFormDataDO formData;
        private final PageRangeInfo pageRange;

        public DocumentGroup(ProjectTaskFormDataDO formData, PageRangeInfo pageRange) {
            this.formData = formData;
            this.pageRange = pageRange;
        }

        public ProjectTaskFormDataDO getFormData() {
            return formData;
        }

        public PageRangeInfo getPageRange() {
            return pageRange;
        }
    }

    /**
     * 从文件名中提取页号信息
     *
     * @param fileName 文件名
     * @return 用于排序的字符串
     */
    private String extractPageNumberFromFileName(String fileName) {
        if (StringHelper.isEmpty(fileName)) {
            return "0000";
        }

        // 🔍 尝试提取数字部分用于排序
        String numberPart = fileName.replaceAll("[^0-9]", "");
        if (StringHelper.isEmpty(numberPart)) {
            return fileName; // 如果没有数字，直接返回文件名
        }

        // 🔍 补齐到4位数字，便于排序
        try {
            int pageNum = Integer.parseInt(numberPart);
            return String.format("%04d", pageNum);
        } catch (NumberFormatException e) {
            return fileName;
        }
    }

    /**
     * 根据页号查找对应的图像
     *
     * @param imageList 图像列表
     * @param pageNumber 页号
     * @return 匹配的图像，如果未找到返回null
     */
    private ProjectTaskImageDataDO findImageByPageNumber(List<ProjectTaskImageDataDO> imageList, int pageNumber) {
        // 🔍 简单实现：假设图像按顺序排列，第N张图像对应第N页
        if (pageNumber > 0 && pageNumber <= imageList.size()) {
            return imageList.get(pageNumber - 1);
        }

        // 🔍 更复杂的匹配逻辑：尝试从文件路径中匹配页号
        String targetPageStr = String.valueOf(pageNumber);
        for (ProjectTaskImageDataDO image : imageList) {
            String filePath = image.getImageFilePath();
            if (filePath != null && filePath.contains(targetPageStr)) {
                // 🔍 进一步验证是否真的匹配页号
                String numberPart = filePath.replaceAll("[^0-9]", "");
                if (targetPageStr.equals(numberPart)) {
                    return image;
                }
            }
        }

        return null;
    }

    /**
     * 克隆图像数据对象
     *
     * @param original 原始图像数据
     * @return 克隆的图像数据
     */
    private ProjectTaskImageDataDO cloneImageData(ProjectTaskImageDataDO original) {
        ProjectTaskImageDataDO cloned = new ProjectTaskImageDataDO();
        cloned.setId(original.getId());
        cloned.setTaskId(original.getTaskId());
        cloned.setTaskConfigId(original.getTaskConfigId());
        cloned.setImageFilePath(original.getImageFilePath());
        cloned.setDataKey(original.getDataKey());
        cloned.setPartNumber(original.getPartNumber());
        cloned.setImageCount(original.getImageCount());
        cloned.setImageSize(original.getImageSize());
        cloned.setPageSizeCount(original.getPageSizeCount());
        cloned.setSuffixCount(original.getSuffixCount());
        cloned.setSuccess(original.getSuccess());
        cloned.setMateIs(original.getMateIs());
        cloned.setIsCheck(original.getIsCheck());
        cloned.setImageNames(original.getImageNames());
        cloned.setCreateTime(original.getCreateTime());
        return cloned;
    }
}