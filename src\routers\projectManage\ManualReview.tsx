import { CloseOutlined } from '@ant-design/icons'
import React, { useEffect, useState } from 'react'
import { useMutation, useQuery } from 'react-query'
import { useLocation, useNavigate, useParams } from 'react-router-dom'
import { useHotkeys } from 'react-hotkeys-hook'
import classNames from 'classnames'
import {
  Button,
  Form,
  Input,
  Select,
  Menu,
  MenuProps,
  Modal,
  Tooltip,
  message,
} from 'antd'
import FormItem from 'antd/es/form/FormItem'
import { ItemType } from 'antd/lib/menu/hooks/useItems'
import Table, { ColumnsType } from 'antd/lib/table'
import TextArea from 'antd/es/input/TextArea'
import {
  SaveFieldLayoutType,
  allocation,
  allocationGetById,
  getCount,
  getFile,
  getImages,
  getLayout,
  getLibraryByTaskId,
  getPieces,
  taskConfigGetByTaskId,
  taskInfoGetById,
  userCheckSave,
  taskDataSaves,
} from '../../api/project'
import { extractResponse } from '../../api/util'
import ImageCanvas from '../../components/project/ImageCanvas'
import { useDiyHotkeyWithTypes } from '../../hooks/useDiyHotkey'
import SetFieldLayout from '../../components/project/SetFieldLayout'
import { getSearch } from '../../utils'
import { useUserContext } from '../../hooks/useUser'

export interface FieldPosition {
  col: number
  row: number
  cols: number
}

interface FieldLayoutConfig extends FieldPosition {
  name: string
}

export default function ManualReview() {
  const form = Form.useForm()[0]
  const navigate = useNavigate()
  const location = useLocation()
  const { id: projectId } = useParams()
  const [imageList, setImageList] = useState<ImageType[]>([])
  const [activeImageIndex, setActiveImageIndex] = useState<number>(0)
  const [activeImage, setActiveImage] = useState<ImageType>()
  const queryString = location.search
  const id = getSearch(queryString, 'id')
  const [allocationData, setAllocationData] = useState<
    UserCheckType | undefined
  >()
  const [configList, setConfigList] = useState<ItemType[]>([])
  const [activeConfig, setActiveConfig] = useState<string>('')
  const [volumePieceType, setVolumePieceType] = useState<'form' | 'list'>(
    'form'
  )
  const [showErrorReason, setShowErrorReason] = useState<string | undefined>()
  const [openErrorModel, setOpenErrorModel] = useState<boolean>(false)
  const [errorReason, setErrorReason] = useState('')
  const [allowEdit,setAllowEdit] = useState(true)
  const [fileObj, setFileObj] = useState<FieldInspectType>()
  const [pieceList, setPieceList] = useState<FieldInspectType[]>([])
  const [taskJson,setTaskJson] = useState<{ [key: string]: string; }>({})
  // 表单用
  const [fieldList, setFieldList] = useState<
    { label: string; value: string }[]
  >([])
  // 卷内所有字段
  const [allPieceFieldList, setAllPieceFieldList] = useState<
    Record<string, string>[]
  >([])

  const [tableFieldList, setTableFieldList] = useState<
    { label: string; value: string }[]
  >([])
  console.log({ fieldList })
  const [activePiece, setActivePiece] = useState<string>('')

  const [openFormLayout, setOpenFormLayout] = React.useState(false)
  const { user } = useUserContext()

  // getLibraryByTaskId
  const { data: libraryFieldList } = useQuery(
    ['getLibraryById', 'userCheck', projectId],
    projectId
      ? extractResponse(() => getLibraryByTaskId(projectId))
      : () => undefined,
    {
      enabled: !!projectId,
    }
  )

  const { MAN_CHECK_PASS } = useDiyHotkeyWithTypes()

  useHotkeys(MAN_CHECK_PASS.key, () =>
    userCheckSaveMutation.mutate({
      ...allocationData!,
      isPass: 1,
      checkStatus: 3,
    })
  )

  document.onkeydown = function (event) {
    if (event.key === 'ArrowRight' || event.key === 'ArrowLeft') {
      event.preventDefault()
    }
  }

  useEffect(() => {
    console.log('=== Form data useEffect triggered ===')
    console.log('activePiece:', activePiece)
    console.log('activeImage?.piece:', activeImage?.piece)
    console.log('activeConfig:', activeConfig)

    if (!libraryFieldList) {
      console.log('libraryFieldList is not available')
      return
    }
    let map: Record<string, string> = {}
    if (activeConfig === 'catalog') {
      if (fileObj?.taskJson) {
        map = JSON.parse(fileObj?.taskJson)
      }
    } else {
      const partNumber =
        activePiece || activeImage?.piece || pieceList[0]?.partNumber
      console.log('Calculated partNumber:', partNumber)

      const piece = pieceList?.find(item => item.partNumber === partNumber)
      console.log('Found piece:', piece)

      if (piece && piece?.taskJson) {
        map = JSON.parse(piece?.taskJson)
        console.log('Parsed taskJson:', map)
      }
    }

    const arr: { label: string; value: string }[] = []
    for (const i in map) {
      arr.push({ label: i, value: map[i] })
    }
    const lastArr: { label: string; value: string }[] = []
    const newObject: { [key: string]: string; }={}

    libraryFieldList.forEach(item => {
      const field = arr.find(name => name.label === item.fieldName)
      if (field) {
        lastArr.push(field)
        newObject[field.label] = field.value
      }
    })

    setTaskJson(newObject)
    setFieldList(lastArr)
  }, [
    activeConfig,
    activeImage?.piece,
    fileObj?.taskJson,
    pieceList,
    activeImage,
    libraryFieldList,
    activePiece,
  ])

  useEffect(() => {
    pieceList?.forEach(item =>
      setAllPieceFieldList(p => [
        ...p,
        { piece: item.partNumber, ...JSON.parse(item.taskJson || '') },
      ])
    )
  }, [pieceList])

  useEffect(() => {
    if (!libraryFieldList) {
      return
    }
    if (allPieceFieldList[0]) {
      const piece = allPieceFieldList[0]
      const arr: { label: string; value: string }[] = []
      for (const i in piece) {
        if (i !== 'piece') {
          arr.push({ label: i, value: piece[i] })
        }
      }
      const lastArr: { label: string; value: string }[] = []
      libraryFieldList.forEach(item => {
        console.log(item.fieldName)
        const field = arr.find(name => item.fieldName === name.label)
        console.log(field,7777777777)
        if (field) {
          lastArr.push(field)
        }
      })
      setTableFieldList(lastArr)
    }
  }, [allPieceFieldList, libraryFieldList])

  const { data: taskInfo } = useQuery(
    ['taskInfoGetById', projectId],
    projectId
      ? extractResponse(() => taskInfoGetById(projectId))
      : () => undefined,
    {
      enabled: !!projectId,
      onSuccess(res) {
        console.log({ res })
      },
    }
  )

  const { data: layout, refetch: layoutRefetch } = useQuery(
    ['getLayout', projectId, activeConfig],
    projectId
      ? extractResponse(() => getLayout(projectId, activeConfig))
      : () => undefined,
    {
      enabled: !!projectId && !!activeConfig,
      onSuccess(res) {
        console.log({ res },)
      },
    }
  )

  // getLayout

  const { refetch } = useQuery(
    ['allocation', projectId],
    projectId ? extractResponse(() => allocation(projectId)) : () => undefined,
    {
      enabled: !!projectId && !id,
      onSuccess(res) {
        setAllocationData(res)
        if(res?.errorType){
          setShowErrorReason(res?.errorType)
          setAllowEdit(false)
        }
        if(res?.remarks){
          setErrorReason(res?.remarks)
          setAllowEdit(false)
        }
      },
      onError(res) {
        navigate(-1)
      },
    }
  )

  useQuery(
    ['allocation', id],
    id ? extractResponse(() => allocationGetById(id)) : () => undefined,
    {
      enabled: !!id,
      onSuccess(res) {
        setAllocationData(res)
        if(res?.errorType){
          setShowErrorReason(res?.errorType)
          setAllowEdit(false)
        }
        if(res?.remarks){
          setErrorReason(res?.remarks)
          setAllowEdit(false)
        }
      },
      onError(res) {
        navigate(-1)
      },
    }
  )

  useQuery(
    ['getImages', projectId, allocationData],
    projectId && allocationData?.dataKey
      ? extractResponse(() => getImages(projectId, allocationData?.dataKey))
      : () => undefined,
    {
      enabled: !!projectId && !!allocationData?.dataKey,
      onSuccess(res) {
        if (res) {
          const list = res.reduce((map, item) => {
            item.images.forEach(image => {
              map.push({ ...image, piece: item.partNumber })
            })
            return map
          }, [] as ImageType[])

          setImageList(list)
          console.log({ list, res })
          if (list.length > 0) {
            setActiveImage(list[0])
            setActiveImageIndex(0)
          }
        }
      },
    }
  )

  const { data: pass } = useQuery(
    ['getCount', projectId, allocationData],
    projectId && allocationData?.dataKey
      ? extractResponse(() => getCount(projectId, allocationData?.dataKey))
      : () => undefined,
    {
      enabled: !!projectId && !!allocationData?.dataKey,
      onSuccess(res) {},
    }
  )

  useQuery(
    ['taskConfigGetByTaskId', projectId],
    extractResponse(() => taskConfigGetByTaskId(projectId!)),
    {
      onSuccess(res) {
        const value = res.filter(
          item => item.ruleConfigType === 1 || item.ruleConfigType === 0
        )
        const piece = value.find(item => item.ruleConfigType === 1)
        const catalog = value.find(item => item.ruleConfigType === 0)
        const list = []
        if (catalog) {
          list.push({
            label: `案卷目录`,
            key: `catalog`,
          })
        }
        if (piece) {
          list.push({
            label: `卷内目录`,
            key: `piece`,
          })
        }

        setConfigList(list)
        if (list.length > 0) {
          setActiveConfig(list[0].key)
        }
      },
    }
  )

  // 卷内目录
  useQuery(
    ['getPieces', projectId, allocationData?.dataKey],
    projectId && allocationData?.dataKey
      ? extractResponse(() => getPieces(projectId, allocationData?.dataKey))
      : () => undefined,
    {
      enabled: !!projectId && !!allocationData?.dataKey,
      onSuccess(res) {
        if (res) {
          setPieceList(res)
          if (res[0].partNumber) {
            setActivePiece(res[0].partNumber)
          }
        }
      },
    }
  )

  // 案卷目录
  useQuery(
    ['getFile', projectId, allocationData?.dataKey],
    projectId && allocationData?.dataKey
      ? extractResponse(() => getFile(projectId, allocationData?.dataKey))
      : () => undefined,
    {
      enabled: !!projectId && !!allocationData?.dataKey,
      onSuccess(res) {
        console.log(res)
        setFileObj(res)
      },
    }
  )

  const columns: ColumnsType<any> = [
    {
      title: '序号',
      render: item => (
        <div
          className="cursor-pointer text-primary-default"
          onClick={() => {
            setActivePiece(item.piece)
            for (let i = 0; i < imageList.length; i++) {
              if (imageList[i].piece === item.piece) {
                setActiveImage(imageList[i])
                setActiveImageIndex(i)
                setVolumePieceType('form')

                return
              }
            }
            setVolumePieceType('form')
          }}
        >
          {item.piece}
        </div>
      ),
      key: 'piece',
      className: 'whitespace-nowrap',
    },
    ...tableFieldList.map(field => ({
      title: field.label,
      render: (item: any) => item[field.label],
      key: field.label,
      className: 'whitespace-nowrap',
    })),
  ]

  const items = [
    {
      value: "档案实体",
      key: '档案实体',
    },
    {
      value: "数字化副本",
      key: '数字化副本',
    },
    {
      value: "筛密",
      key: '筛密',
    },
    {
      value: "OCR",
      key: 'OCR',
    },
    {
      value: "条目选项",
      key: '条目选项',
    },
  ]

  function getSortedFields(
    fieldList: { label: string; value: string }[],
    layoutData?: SaveFieldLayoutType
  ) {
    const layoutDataResult = JSON.parse(
      layoutData?.layout || '[]'
    ) as FieldLayoutConfig[]

    const fieldListWithLayout: {
      label: string
      value: string
      col: number
      row: number
      cols: number
    }[] = fieldList.map(item => ({
      ...item,
      col: 0,
      cols: 0,
      row: 0,
    }))

    if (layoutDataResult.length > 0) {
      for (let i = 0; i < fieldList.length; i++) {
        for (let j = 0; j < layoutDataResult.length; j++) {
          if (fieldList[i].label === layoutDataResult[j].name) {
            const { col, cols, row } = layoutDataResult[j]
            fieldListWithLayout[i] = { col, cols, row, ...fieldList[i] }
          }
        }
      }
    }

    const rowArr = fieldListWithLayout.filter(item => {
      return item.row
    })
    const noRowArr = fieldListWithLayout.filter(item => {
      return !item.row
    })
    rowArr.sort((v1, v2) => {
      if (v1.row === v2.row) {
        return v1.col - v2.col
      } else {
        return v1.row - v2.row
      }
    })
    let newArr: {
      label: string
      value: string
      col: number
      row: number
      cols: number
    }[][] = []
    if (rowArr.length > 0) {
      rowArr.forEach(item => {
        newArr[item.row] = newArr[item.row] || []
        newArr[item.row].push(item)
      })
    }
    newArr = newArr.filter(item => item.length > 0)

    return [...newArr, ...noRowArr.map(item => [item])]
  }

  function computeWidth(item: {
    label: string
    value: string
    col: number
    row: number
    cols: number
  }) {
    const cols = item.cols
    if (!cols) return 100
    if (cols === 1) {
      return 33.3
    } else if (cols === 2) {
      return 66.6
    } else if (cols === 3) {
      return 100
    }
  }

  function renderForm() {
    const fieldListArr = getSortedFields(fieldList || [], layout)
    console.log(fieldListArr,44444444)
    return fieldListArr.map((fieldList,fieldListIndex) => (
      <Form key={`${activePiece}-${fieldListIndex}`} form={form} layout="vertical">
        <div className="flex">
          {fieldList.map((field,fieldIndex) => (
            <FormItem
              label={field.label}
              style={{
                width: `${computeWidth(field)}%`,
              }}
              className="!px-0.5"
            >
              <Input.TextArea
                autoSize={true}
                value={field.value}
                className=" !text-black"
                onChange={e=>{
                  const newFieldListArr = fieldListArr;
                  newFieldListArr[fieldListIndex][fieldIndex]["value"] = e.target.value
                  const newObject: { [key: string]: string; }={}
                  newFieldListArr.forEach(newFieldList=>{
                    newFieldList.forEach(newField=>{
                      newObject[newField.label] = newField.value
                    })
                  })
                  setTaskJson(newObject)
                }}
              />
            </FormItem>
          ))}
        </div>
      </Form>
    ))
  }

  const userCheckSaveMutation = useMutation(userCheckSave, {
    onSuccess(res) {
      console.log(22222)
      message.success(res.data.message)
      setShowErrorReason(undefined)
      setErrorReason('')
      setAllowEdit(true)
      setOpenErrorModel(false)
      setAllPieceFieldList([])
      setActiveImageIndex(0)
      setActivePiece('')
      console.log(77777777)
      refetch()
    },
  })

  const taskDataSavesMutation = useMutation(taskDataSaves,{
    onSuccess(res) {
      message.success(res.data.message)
    },
  })

  function switchPiece(type: 'left' | 'right') {
    console.log('switchPiece called with type:', type)
    console.log('pieceList:', pieceList)
    console.log('activePiece:', activePiece)
    console.log('imageList:', imageList)

    if (!pieceList || pieceList.length === 0) {
      console.log('pieceList is empty or undefined')
      return
    }

    let index = 0
    for (let i = 0; i < pieceList.length; i++) {
      if (pieceList[i].partNumber === activePiece) {
        index = i
        break
      }
    }

    console.log('current index:', index)

    if (type === 'left') {
      if (index !== 0) {
        index = index - 1
      }
    } else {
      if (index !== pieceList.length - 1) {
        index = index + 1
      }
    }

    console.log('new index:', index)
    console.log('target partNumber:', pieceList[index]?.partNumber)

    // 确保 partNumber 存在且不为空
    const targetPartNumber = pieceList[index]?.partNumber
    if (!targetPartNumber) {
      console.error('Target partNumber is undefined or empty')
      return
    }

    // 查找对应的图像
    for (let i = 0; i < imageList.length; i++) {
      if (imageList[i].piece === targetPartNumber) {
        console.log('Found matching image at index:', i)
        setActiveImage(imageList[i])
        setActiveImageIndex(i)
        setActivePiece(targetPartNumber)
        return
      }
    }

    // 如果没有找到对应的图像，仍然更新 activePiece
    console.log('No matching image found, updating activePiece only')
    setActivePiece(targetPartNumber)
  }

  function togglePage(type: 'left' | 'right') {
    if (activeImageIndex === undefined || isNaN(activeImageIndex) || !imageList.length) {
      return
    }
    const index = activeImageIndex
    if (type === 'left') {
      if (index !== 0) {
        setActiveImageIndex(index - 1)
        setActiveImage(imageList[index - 1])
      }
    } else {
      if (index < imageList.length - 1) {
        setActiveImageIndex(index + 1)
        setActiveImage(imageList[index + 1])
      }
    }
  }

  const clickOk = () => {
    if (allowEdit){
      if (!showErrorReason) {
        message.warning('请选择错误类型')
        return
      }
      if (!errorReason) {
        message.warning('请填写错误原因')
        return
      }
      userCheckSaveMutation.mutate({
        ...allocationData!,
        isPass: 0,
        remarks: errorReason,
        errorType: showErrorReason,
        checkUser: user?.id,
        checkUserName: user?.name,
        checkStatus: 3,
        allocationState: 1,
      })
      console.log(errorReason, showErrorReason)
    }
  }

  const clickCanel = () =>{
    setOpenErrorModel(false)
    // setAllowEdit(false)
    // setShowErrorReason(undefined)
    // setErrorReason('')
  }

  const confirmBtnStyle = {
    backgroundColor: '#f56c6c', // 设置确认按钮的背景颜色
    color: '#fff', // 设置确认按钮的文本颜色
    border: 'none', // 设置确认按钮的边框样式
    padding: '10px 20px', // 设置确认按钮的内边距
    fontSize: '16px', // 设置确认按钮的字体大小
    borderRadius: '5px', // 设置确认按钮的圆角样式
  };

  return (
    <div className="h-full">
      <div className="h-14 border-b flex items-center pl-6 text-[#666666]">
        <CloseOutlined
          className="mr-3 cursor-pointer text-opacity-40 fill-current text-black"
          onClick={() => {
            navigate(-1)
          }}
        />
        {taskInfo?.taskName}
      </div>
      <div className="h-12 border-b px-6 flex items-center">
        <div>
          {allocationData?.dataKey}
          {/* ({(allocationData?.index || 0) + 1}/
          {allocationData?.count || 0}) */}
        </div>
        <div className="flex items-center ml-auto">
          <div onClick={()=>{setOpenErrorModel(true)}}>
            <Button danger>不合格</Button>
          </div>
          <div className="text-[#FF4D4F] ml-2">{pass?.error || 0}</div>
          <Tooltip
            title="↵"
            color="white"
            overlayInnerStyle={{ color: `black` }}
          >
            <Button
              className="ml-4"
              type="primary"
              onClick={() => {
                let new_allocationData = allocationData
                if (new_allocationData){
                  new_allocationData.errorType = undefined
                  new_allocationData.remarks = ""
                }
                if (projectId&&allocationData?.dataKey){
                  taskDataSavesMutation.mutate({
                    task_id:projectId,
                    param:taskJson,
                    dataKey:allocationData?.dataKey
                  })
                }

                setShowErrorReason(undefined)
                setErrorReason('')
                setAllowEdit(true)
                setAllocationData(new_allocationData)
                userCheckSaveMutation.mutate({
                  ...new_allocationData!,
                  checkUser: user?.id,
                  checkUserName: user?.name,
                  isPass: 1,
                  checkStatus: 3,
                  allocationState: 1,
                })
              }}
            >
              合格
            </Button>
          </Tooltip>
          <div className="text-[#52C41A] ml-2">{pass?.ok || 0}</div>
          <Button
            href={'/api/exactness/userCheck/reportPdf?taskId=' + projectId}
            className="ml-4"
          >
            导出报告
          </Button>
        </div>
      </div>
      <div className="flex " style={{ height: 'calc(100% - 103px)' }}>
        <div className="w-[280px]">
          <div className="flex justify-between h-8 bg-[#FAFAFA] px-4 items-center text-sm border-b">
            <div>图像列表</div>
            <div>{(isNaN(activeImageIndex) ? 0 : activeImageIndex) + 1 + '/' + imageList.length}</div>
          </div>
          <div className="h-[756px] px-1 pt-2 overflow-y-auto">
            {imageList.map((image, index) => (
              <div
                className={classNames(
                  'h-10 cursor-pointer  px-4 rounded-sm flex items-center',
                  activeImageIndex === index
                    ? `bg-[#FF7F37] bg-opacity-10 text-primary-default  `
                    : null
                )}
                onClick={() => {
                  setActiveImageIndex(index)
                  setActiveImage(image)
                }}
              >
                <div className="w-[201px] leading-10 truncate">{image.key}</div>
                {/* <div className="w-2 h-2 rounded-full bg-[#F5222D] ml-auto" /> */}
              </div>
            ))}
          </div>
        </div>
        <ImageCanvas
          isOpen={true}
          activeImageIndex={activeImageIndex}
          setActiveImageIndex={setActiveImageIndex}
          imageList={imageList}
          switchPiece={switchPiece}
          allocationData={allocationData}
          togglePage={togglePage}
        />
        <div className="w-[464px] overflow-hidden">
          <div>
            <Menu
              onClick={e => {
                setActiveConfig(e.key)
                setVolumePieceType('form')
              }}
              onKeyDown={e => {
                if (e.key === 'ArrowRight' || e.key === 'ArrowLeft') {
                  e.preventDefault()
                }
                return
              }}
              selectedKeys={[activeConfig]}
              mode="horizontal"
              items={configList}
            />
          </div>
          <div className="p-4 overflow-y-auto h-[768px]">
            {activeConfig === 'piece' ? (
              <div className="h-11 rounded flex items-center mb-3">
                {(['form', 'list'] as const).map(type => (
                  <Button
                    key={type}
                    onClick={() => {
                      setVolumePieceType(type)
                    }}
                    className="mr-7"
                    type={type === volumePieceType ? 'primary' : undefined}
                  >
                    {type === 'form' ? '表单详情' : '表单列表'}
                  </Button>
                ))}
              </div>
            ) : null}
            {volumePieceType === 'list' ? (
              <Table
                columns={columns}
                dataSource={allPieceFieldList}
                pagination={false}
              />
            ) : (
              <>
                <Button
                  className="w-full mb-4 mt-2"
                  type="primary"
                  onClick={() => setOpenFormLayout(true)}
                >
                  点击调整表单详情布局
                </Button>
                {renderForm()}
              </>
            )}
          </div>
        </div>
      </div>
      <Modal
        title="错误原因"
        open={openErrorModel}
        footer={
          [
            <Button key="cancel"  onClick ={()=>{clickCanel();
              if (projectId&&allocationData?.dataKey){
                taskDataSavesMutation.mutate({
                  task_id:projectId,
                  param:taskJson,
                  dataKey:allocationData?.dataKey
                })
              }
            }}>
              取消
            </Button>,
            allowEdit?(<Button key="submit" disabled={!allowEdit} type={"primary"}
                               onClick={()=>{
                                 if (projectId&&allocationData?.dataKey){
                                   taskDataSavesMutation.mutate({
                                     task_id:projectId,
                                     param:taskJson,
                                     dataKey:allocationData?.dataKey
                                   })
                                 };clickOk()
                               }}
            >
              确认
            </Button>):(<Button key="submit" disabled={!allowEdit} className="!cursor-not-allowed" type={"primary"}>
              确认
            </Button>)
          ]
        }
        onCancel={() => {
          clickCanel()
        }}
      >
        <FormItem
          label={
            <div className="flex">
              <span
                className="leading mt-0.5"
                style={{
                  display: `inline-block`,
                  marginRight: `4px`,
                  color: `#ff4d4f`,
                  fontSize: `14px`,
                }}
              >
                *
              </span>
              错误类型
            </div>
          }
        >
          <Select
            className="!w-[390px]"
            value={showErrorReason?.split(",")}
            mode={"multiple"}
            allowClear={true}
            disabled={!allowEdit}
            onChange={value => {
              const _value = value.toString()
              if (_value!==""){
                setShowErrorReason(_value)
              }else{
                setShowErrorReason(undefined)
              }

            }}
          >
            {
              items.map((item)=>{
                return (
                  <option key={item.key} value={item.key}>
                    {item.value}
                  </option>
                )
              })
            }
          </Select>
        </FormItem>

        <FormItem
          label={
            <div className="flex">
              <span
                className="leading mt-0.5"
                style={{
                  display: `inline-block`,
                  marginRight: `4px`,
                  color: `#ff4d4f`,
                  fontSize: `14px`,
                }}
              >
                *
              </span>
              错误原因
            </div>
          }
        >
          <Input
            disabled={!allowEdit}
            value={errorReason}
            onChange={e => {
              setErrorReason(e.target.value)
            }}
          />
        </FormItem>

      </Modal>
      <SetFieldLayout
        taskId={projectId!}
        isOpenFormLayout={openFormLayout}
        fieldList={fieldList}
        onClose={refresh => {
          setOpenFormLayout(false)
          // if (refresh) {
          //   return activeSpecialForm ? reloadSpecialLayout() : reloadLayout()
          // }
        }}
        layoutData={layout}
        activeConfig={activeConfig}
        layoutRefetch={layoutRefetch}
      />
    </div>
  )
}
